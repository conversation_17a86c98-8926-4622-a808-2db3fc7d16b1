// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id          String   @id @default(cuid())
  clerkUserId String   @unique
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  imageUrl  String?
  firstName String?
  lastName  String? // Fixed typo

  email String @unique // Made required

  credits Int @default(150)

  UserToProject UserToProject[]
}

model Project {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  name      String
  githubUrl String
  deletedAt DateTime? // Fixed typo

  UserToProject UserToProject[]
  Commit        Commit[]
}

model UserToProject {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  userId    String
  projectId String

  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@unique([userId, projectId])
}

model Commit {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updateAt  DateTime @updatedAt

  projectId String
  project   Project @relation(fields: [projectId], references: [id])

  commitMessage      String
  commitHash         String
  commitAuthorAvator String
  commitDate         DateTime

  summary String
}
