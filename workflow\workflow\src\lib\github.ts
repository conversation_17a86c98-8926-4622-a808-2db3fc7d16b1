import { Github } from 'lucide-react';
import { Octokit } from 'octokit';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const octokit = new Octokit({
  auth: process.env.GITHUB_TOKEN,
});

const githubUrl = 'https://github.com/docker/genai-stack';

type Response = {
  commitHash: string;
  commitMessage: string;
  commitAuthorName: string;
  commitAuthorAvatar: string;
  commitDate: string;
};

export const getCommitHashes = async (githubUrl: string): Promise<Response[]> => {
  // Extract owner and repo from URL
  const urlParts = githubUrl.split('/');
  const owner = urlParts[3];
  const repo = urlParts[4];

  const { data } = await octokit.rest.repos.listCommits({
    owner,
    repo,
  });

  const sortedCommits = data.sort((a: any, b: any) => 
    new Date(b.commit.author.date).getTime() - new Date(a.commit.author.date).getTime()
  );

  return sortedCommits.slice(0, 15).map((commit: any) => ({
    commitHash: commit.sha,
    commitMessage: commit.commit.message ?? "",
    commitAuthorName: commit.commit?.author?.name ?? "",
    commitAuthorAvatar: commit?.author?.avatar_url ?? "",
    commitDate: commit.commit?.author.date,
  }));
};

export const pollCommits = async (projectId: string) =>{
  const {project, githubUrl} =  await fetchProjectGithubUrl(projectId)
        const commits = await getCommitHashes(githubUrl);
        return commits;
async function fetchProjectGithubUrl(projectId: string) {
    const project = await prisma.project.findUnique({
      where: {
        id: projectId,
      },
      select:{
        githubUrl: true,
      }
    });
    return { project ,githubUrl : project?.githubUrl};
}

async function filterUnprocessedCommits(projectId: string, commitedHashes:Response[]){
  const processedCommits = await prisma.commit.findMany({
    where: {
      projectId,
    },
  })
  const unprocessedCommits = commitedHashes.filter((commit) => !processedCommits.find((c) => c.commitHash === commit.commitHash));
  return unprocessedCommits;
}
}